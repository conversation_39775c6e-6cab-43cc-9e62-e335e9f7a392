import { logger } from "firebase-functions/v2";
import { LogOperations } from "../constants";

export const AdminCollectionLogger = {
  logAdminCollectionOperation({
    operation,
    collectionId,
    updatedCount,
    userId,
  }: {
    operation: LogOperations;
    collectionId: string;
    updatedCount: number;
    userId: string;
  }) {
    logger.info(
      `Admin collection operation: ${operation} for collection ${collectionId}, updated ${updatedCount} orders`,
      {
        operation,
        service: "admin-collection-function",
        collectionId,
        updatedCount,
        userId,
      }
    );
  },
};

export function logAdminCollectionOperation({
  operation,
  collectionId,
  updatedCount,
  userId,
}: {
  operation: LogOperations;
  collectionId: string;
  updatedCount: number;
  userId: string;
}) {
  logger.info(
    `Admin collection operation: ${operation} for collection ${collectionId}, updated ${updatedCount} orders`,
    {
      operation,
      service: "admin-collection-function",
      collectionId,
      updatedCount,
      userId,
    }
  );
}

export function logAdminCollectionError({
  error,
  operation,
  collectionId,
  userId,
}: {
  error: unknown;
  operation: LogOperations;
  collectionId?: string;
  userId?: string;
}) {
  logger.error(`Error in admin collection operation: ${operation}`, {
    operation,
    service: "admin-collection-function",
    error,
    collectionId,
    userId,
  });
}
