import {
  OrderEntity,
  ORDERS_COLLECTION_NAME,
} from "../../mikerudenko/marketplace-shared";
import {
  processOrderCancellation,
  validateCancellationPermission,
} from "../../services/order-cancellation-service/order-cancellation-service";
import {
  throwCancellationFailed,
  throwOrderNotFound,
} from "./general-order-function.error-handler";
import * as admin from "firebase-admin";

const db = admin.firestore();

export interface CancelOrderParams {
  orderId: string;
  userId: string;
}

export interface CancelOrderResult {
  success: boolean;
  message: string;
  order: {
    id: string;
    number: number;
    status: string;
  };
  feeApplied: number;
  feeType: string;
}

export async function cancelOrder({
  orderId,
  userId,
}: CancelOrderParams): Promise<CancelOrderResult> {
  const orderDoc = await db
    .collection(ORDERS_COLLECTION_NAME)
    .doc(orderId)
    .get();

  if (!orderDoc.exists) {
    throwOrderNotFound();
  }

  const order = { id: orderDoc.id, ...orderDoc.data() } as OrderEntity;

  try {
    // Validate cancellation permission using the service
    await validateCancellationPermission(order, userId);

    // Process the cancellation using unified logic
    const result = await processOrderCancellation(order, userId);

    return {
      success: result.success,
      message: result.message,
      order: {
        id: order.id!,
        number: order.number,
        status: "cancelled",
      },
      // @ts-expect-error note
      feeApplied: result?.feeApplied ?? 0,
      feeType: result.feeType,
    };
  } catch (error) {
    if (error instanceof Error) {
      throwCancellationFailed(error.message);
    }
    throw error;
  }
}
