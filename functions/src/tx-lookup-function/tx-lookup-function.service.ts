import * as admin from "firebase-admin";
import { TxLookupEntity } from "../mikerudenko/marketplace-shared";

import {
  throwTxLookupGetError,
  throwTxLookupUpdateError,
} from "./tx-lookup-function.error-handler";
import { TxLookupLogger } from "./tx-lookup-function.logger";
const db = admin.firestore();

const TX_LOOKUP_COLLECTION = "tx_lookup";
const TX_LOOKUP_DOC_ID = "main";

export async function getTxLookup(): Promise<TxLookupEntity | null> {
  try {
    const doc = await db
      .collection(TX_LOOKUP_COLLECTION)
      .doc(TX_LOOKUP_DOC_ID)
      .get();

    if (!doc.exists) {
      return null;
    }

    return {
      id: doc.id,
      ...doc.data(),
    } as TxLookupEntity;
  } catch (error) {
    TxLookupLogger.logTxLookupGetError({ error });
    throwTxLookupGetError((error as any).message);
  }
}

export async function updateTxLookup(
  lastCheckedRecordId: string
): Promise<void> {
  try {
    await db.collection(TX_LOOKUP_COLLECTION).doc(TX_LOOKUP_DOC_ID).set(
      {
        last_checked_record_id: lastCheckedRecordId,
        updatedAt: admin.firestore.FieldValue.serverTimestamp(),
      },
      { merge: true }
    );

    TxLookupLogger.logTxLookupUpdated({ lastCheckedRecordId });
  } catch (error) {
    TxLookupLogger.logTxLookupUpdateError({ error, lastCheckedRecordId });
    throwTxLookupUpdateError((error as any).message);
  }
}
