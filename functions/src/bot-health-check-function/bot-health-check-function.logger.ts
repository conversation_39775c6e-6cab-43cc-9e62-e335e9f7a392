import { logger } from "firebase-functions/v2";
import { LogOperations } from "../constants";

export function logHealthCheckStarted({ status }: { status: string }) {
  logger.info("Bot health check started", {
    operation: LogOperations.BOT_HEALTH_CHECK,
    service: "bot-health-check-function",
    status,
  });
}

export function logHealthCheckCall({
  healthCheckUrl,
  hasAuthentication,
}: {
  healthCheckUrl: string;
  hasAuthentication: boolean;
}) {
  logger.info("Making health check call", {
    operation: LogOperations.BOT_HEALTH_CHECK,
    service: "bot-health-check-function",
    healthCheckUrl,
    hasAuthentication,
  });
}

export function logHealthCheckResponse({
  responseData,
}: {
  responseData: any;
}) {
  logger.info("Health check response received", {
    operation: LogOperations.BOT_HEALTH_CHECK,
    service: "bot-health-check-function",
    responseData,
  });
}

export function logHealthCheckPassed({ status }: { status: string }) {
  logger.info("Bot health check passed", {
    operation: LogOperations.BOT_HEALTH_CHECK,
    service: "bot-health-check-function",
    status,
  });
}

export function logHealthCheckUnhealthy({
  status,
  responseData,
}: {
  status: string;
  responseData: any;
}) {
  logger.info("Bot health check unhealthy", {
    operation: LogOperations.BOT_HEALTH_CHECK,
    service: "bot-health-check-function",
    status,
    responseData,
  });
}

export function logHealthCheckFailed({
  error,
  status,
}: {
  error: unknown;
  status: string;
}) {
  logger.error("Bot health check failed", {
    operation: LogOperations.BOT_HEALTH_CHECK,
    service: "bot-health-check-function",
    error,
    status,
  });
}

export function logMonitorTriggered({
  status,
  timestamp,
}: {
  status: string;
  timestamp: string;
}) {
  logger.info("Bot health monitor triggered", {
    operation: LogOperations.MONITOR,
    service: "bot-health-check-function",
    status,
    timestamp,
  });
}

export function logMonitorCompleted() {
  logger.info("Bot health monitor completed", {
    operation: LogOperations.MONITOR,
    service: "bot-health-check-function",
  });
}

export function logMonitorFailed({
  error,
  status,
}: {
  error: unknown;
  status: string;
}) {
  logger.error("Bot health monitor failed", {
    operation: LogOperations.MONITOR,
    service: "bot-health-check-function",
    error,
    status,
  });
}
