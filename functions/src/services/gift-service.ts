import * as admin from "firebase-admin";

import {
  GiftEntity,
  GIFTS_COLLECTION_NAME,
} from "../mikerudenko/marketplace-shared";

const db = admin.firestore();

export async function getGiftById(giftId: string) {
  const giftDoc = await db.collection(GIFTS_COLLECTION_NAME).doc(giftId).get();

  if (!giftDoc.exists) {
    return null;
  }

  return { id: giftDoc.id, ...giftDoc.data() } as GiftEntity;
}
