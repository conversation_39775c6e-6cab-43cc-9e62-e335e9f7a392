import * as admin from "firebase-admin";
import {
  APP_CONFIG_COLLECTION,
  AppConfigEntity,
} from "../mikerudenko/marketplace-shared";

const db = admin.firestore();

const cache = new Map<string, { data: any; timestamp: number; ttl: number }>();

const DEFAULT_TTL = 5 * 60 * 1000;
const APP_CONFIG_CACHE_KEY = "app_config";

function getCachedData<T>(key: string): T | null {
  const cached = cache.get(key);
  if (!cached) return null;

  const now = Date.now();
  if (now - cached.timestamp > cached.ttl) {
    cache.delete(key);
    return null;
  }

  return cached.data as T;
}

function setCachedData<T>(
  key: string,
  data: T,
  ttl: number = DEFAULT_TTL
): void {
  cache.set(key, {
    data,
    timestamp: Date.now(),
    ttl,
  });
}

export async function getCachedAppConfig(): Promise<AppConfigEntity> {
  const cached = getCachedData<AppConfigEntity>(APP_CONFIG_CACHE_KEY);
  if (cached) {
    return cached;
  }

  const configDoc = await db
    .collection(APP_CONFIG_COLLECTION)
    .doc("config")
    .get();

  if (!configDoc.exists) {
    throw new Error("App config not found");
  }

  const config = configDoc.data() as AppConfigEntity;
  setCachedData(APP_CONFIG_CACHE_KEY, config);

  return config;
}

export function clearCache(key: string): void {
  cache.delete(key);
}

export function clearAllCache(): void {
  cache.clear();
}

export function getCacheStats() {
  const now = Date.now();
  const entries = Array.from(cache.entries()).map(([key, value]) => ({
    key,
    age: now - value.timestamp,
    ttl: value.ttl,
    expired: now - value.timestamp > value.ttl,
  }));

  return {
    totalEntries: cache.size,
    entries,
  };
}
